Resources:
   OapRootAPIGWInvokePermission:
    Type: "AWS::Lambda::Permission"
    Properties:
      FunctionName: arn:aws:lambda:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:function:oap-backend-service-${self:provider.stage}
      Action: "lambda:InvokeFunction"
      Principal: "apigateway.amazonaws.com"
      SourceArn: "arn:aws:execute-api:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:${file(env.${self:provider.stage}.yml):variables.OAP_API_ID}/*/*/oap/*"
   OapAPIGWInvokePermission:
    Type: "AWS::Lambda::Permission"
    Properties:
      FunctionName: arn:aws:lambda:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:function:oap-backend-service-${self:provider.stage}
      Action: "lambda:InvokeFunction"
      Principal: "apigateway.amazonaws.com"
      SourceArn: "arn:aws:execute-api:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:${file(env.${self:provider.stage}.yml):variables.OAP_API_ID}/*/*/oap"
   StudentOapRootAPIGWInvokePermission:
    Type: "AWS::Lambda::Permission"
    Properties:
      FunctionName: arn:aws:lambda:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:function:oap-backend-service-${self:provider.stage}
      Action: "lambda:InvokeFunction"
      Principal: "apigateway.amazonaws.com"
      SourceArn: "arn:aws:execute-api:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:${file(env.${self:provider.stage}.yml):variables.OAP_API_ID}/*/*/student/oap/*"
   StudentOapAPIGWInvokePermission:
    Type: "AWS::Lambda::Permission"
    Properties:
      FunctionName: arn:aws:lambda:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:function:oap-backend-service-${self:provider.stage}
      Action: "lambda:InvokeFunction"
      Principal: "apigateway.amazonaws.com"
      SourceArn: "arn:aws:execute-api:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:${file(env.${self:provider.stage}.yml):variables.OAP_API_ID}/*/*/student/oap"
