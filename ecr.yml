Resources:
  OapBackendECRLifecyclePolicy:
    Type: AWS::ECR::LifecyclePolicy
    Properties:
      RepositoryName: serverless-oap-backend-service-${self:provider.stage}
      LifecyclePolicyText: |
        {
          "rules": [
            {
              "rulePriority": 1,
              "description": "Image expiry rules",
              "selection": {
                "tagStatus": "any",
                "countType": "imageCountMoreThan",
                "countNumber": ${self:provider.stage == 'prod' ? 10 : 3}
              },
              "action": {
                "type": "expire"
              }
            }
          ]
        }
