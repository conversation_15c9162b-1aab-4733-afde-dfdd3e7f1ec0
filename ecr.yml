Resources:
  OapBackendECRRepository:
    Type: AWS::ECR::Repository
    Properties:
      RepositoryName: oap-backend-service-${self:provider.stage}
      ImageTagMutability: MUTABLE
      ImageScanningConfiguration:
        ScanOnPush: true
      Tags:
        - Key: ENVIRONMENT
          Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        - Key: TEAM
          Value: EIP Development Team
        - Key: PROJECT
          Value: OAP

  OapBackendECRLifecyclePolicy:
    Type: AWS::ECR::LifecyclePolicy
    Properties:
      RepositoryName: !Ref OapBackendECRRepository
      LifecyclePolicyText: !Sub |
        {
          "rules": [
            {
              "rulePriority": 1,
              "description": "in prod 10 images and in dev 3 images",
              "selection": {
                "tagStatus": "any",
                "countType": "imageCountMoreThan",
                "countNumber": ${self:provider.stage == 'prod' ? 10 : 3}
              },
              "action": {
                "type": "expire"
              }
            }
          ]
        }
