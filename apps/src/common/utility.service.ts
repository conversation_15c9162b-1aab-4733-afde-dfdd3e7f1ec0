// request-id.middleware.ts
import { Injectable, NestMiddleware } from '@nestjs/common';
import { DynamoDBService } from './dynamodb.service';
import * as AWS from 'aws-sdk';
import { SalesforceService } from './salesforce.service';
@Injectable()
export class UtilityService {
  constructor(
    private dynamoDBService: DynamoDBService,
    private salesforceService: SalesforceService,
  ) {}
  async createAppIdForExistingApplications(request): Promise<any> {
    const dynamoDB = new AWS.DynamoDB.DocumentClient({
      region: process.env.REGION,
    });
    const brand = 'IBAT_EL_';
    const scanParams = {
      TableName: process.env.STUDENT_DETAILS,
      FilterExpression: 'begins_with(SK, :value)',
      ExpressionAttributeValues: {
        ':value': brand,
      },
    };
    const applicationDetails = await dynamoDB.scan(scanParams).promise();
    const applications = applicationDetails.Items;
    const response = {
      successfullyStoredRecords: [],
      recordsalreadyWithAppId: [],
      recordsFailedOnDbUpdate: [],
      recordsFailedOnSfUpdate: [],
      totalApplicationScanned: applications.length,
    };
    applications.sort(
      (a, b) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
    );
    console.log(applications.length);
    let i = 0;
    for (const application of applications) {
      i++;
      if (i > 5) {
        break;
      }
      if (!application.appId) {
        application['appId'] = await this.updateAndGetAppId(application.brand);
        try {
          await this.dynamoDBService.putObject(process.env.STUDENT_DETAILS, {
            Item: {
              PK: application['email'],
              SK: `${application.applicationSource}_${application.applicationId}`,
              ...application,
            },
          });
        } catch (error) {
          response.recordsFailedOnDbUpdate.push(application);
        }
        try {
          await this.salesforceService.postDataToSf(
            `gus/updateappid`,
            {
              applicationId: application.applicationId,
              appId: application['appId'],
            },
            process.env.GUS_MIDDLEWARE_API_KEY,
            request,
          );
          response.successfullyStoredRecords.push(application);
        } catch (error) {
          console.log('errr', error);
          response.recordsFailedOnSfUpdate.push(application);
        }
      } else {
        response.recordsalreadyWithAppId.push(application);
      }
      console.log('i', i);
    }
    response['successCount'] = response.successfullyStoredRecords.length;
    return response;
  }
  async updateAndGetAppId(PK): Promise<any> {
    const updateParams = {
      TableName: process.env.OAP_BRAND_TABLE,
      Key: {
        PK: PK,
      },
      UpdateExpression:
        'SET latestStudentId = if_not_exists(latestStudentId, :start) + :inc',
      ExpressionAttributeValues: {
        ':inc': 1,
        ':start': 0,
      },
      ReturnValues: 'ALL_NEW',
    };
    const response = await this.dynamoDBService.updateObject(updateParams);
    console.log('response', response);
    let studentId = '0000000000';
    studentId = String(
      Number(studentId) + response.Attributes.latestStudentId,
    ).padStart(10, '0');
    return `${response.Attributes.brandCode}${studentId}`;
  }
}
