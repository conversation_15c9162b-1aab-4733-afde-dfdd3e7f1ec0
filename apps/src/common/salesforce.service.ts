import { Injectable, Inject } from '@nestjs/common';
import axios, { AxiosError } from 'axios';
import { CloudWatchLoggerService } from '@gus-eip/loggers';
@Injectable()
export class SalesforceService {
  constructor(
    @Inject('CloudWatchLogger')
    private readonly cloudWatchLoggerService: CloudWatchLoggerService,
  ) { }
  async fetchData(endpoint, apikey, request?): Promise<any> {
    try {
      const apiKey = apikey ? apikey : process.env.GUS_MIDDLEWARE_API_KEY;
      const path = `${process.env.GUS_MIDDLEWARE_API}/salesforce/${endpoint}`;
      const headers = {
        'Content-Type': 'application/json',
        'x-api-key': apiKey,
      };

      const response = await axios.get(path, {
        headers,
      });
      // await this.log(endpoint, request);
      return response.data;
    } catch (error) {
      console.log('error: ', error);
      // await this.error(endpoint, error?.response?.data, 500, request);
      throw error?.response?.data;
    }
  }
  async postData(
    endpoint: string,
    requestData: any,
    apiKey: string,
    request?: any,
  ): Promise<any> {
    try {
      const path = `${process.env.GUS_MIDDLEWARE_API}/salesforce/${endpoint}`;
      const headers = {
        'x-api-key': apiKey ? apiKey : process.env.GUS_MIDDLEWARE_API_KEY,
      };

      const response = await axios.post(path, requestData, { headers });
      // await this.log(endpoint, request, requestData);
      return response.data;
    } catch (error) {
      // await this.error(
      //   endpoint,
      //   error?.response?.data,
      //   500,
      //   request,
      //   requestData,
      // );
      throw error?.response?.data;
    }
  }
  async postDataToSf(
    endpoint: string,
    requestData: any,
    apiKey: string,
    request: any,
  ): Promise<any> {
    try {
      const path = `${process.env.GUS_MIDDLEWARE_API}/salesforce/${endpoint}`;
      const headers = {
        'x-api-key': apiKey ? apiKey : process.env.GUS_MIDDLEWARE_API_KEY,
      };
      const response = await axios.post(path, requestData, { headers });
      return response.data;
    } catch (error) {
      throw error?.response?.data;
    }
  }
  async patchData(endpoint, requestData: any, request): Promise<any> {
    try {
      const path = `${process.env.GUS_MIDDLEWARE_API}/salesforce/${endpoint}`;
      const headers = {
        'x-api-key': process.env.GUS_MIDDLEWARE_API_KEY,
      };

      const response = await axios.patch(path, requestData, { headers });
      // await this.log(endpoint, request, requestData);
      return response.data;
    } catch (error) {
      await this.error(
        endpoint,
        error?.response?.data,
        500,
        request,
        requestData,
      );
      throw error?.response?.data;
    }
  }
  async log(endpoint: string, request, requestData = null): Promise<any> {
    requestData = requestData != null ? requestData : `${endpoint}`;
    // await this.cloudWatchLoggerService.log(
    //   request?.requestId,
    //   new Date().toISOString(),

    //   endpoint,
    //   'SALESFORCE_REQUEST',
    //   200,
    //   requestData,
    //   request?.brand,
    //   `oap-backend/${request?.requestId}`,
    // );
  }
  async error(
    endpoint: string,
    error,
    status,
    request,
    requestData = null,
  ): Promise<any> {
    requestData = requestData != null ? requestData : `${endpoint}`;
    // await this.cloudWatchLoggerService.error(
    //   request?.requestId,
    //   requestData,
    //   'SALESFORCE_REQUEST',
    //   'SALESFORCE_REQUEST_FAILED',
    //   status,
    //   error,
    //   request?.brand,
    //   `oap-backend/${request?.requestId}`,
    // );
  }
}
