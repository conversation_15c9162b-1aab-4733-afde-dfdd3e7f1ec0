// request-id.middleware.ts
import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import * as uuid from 'uuid';
import { DynamoDBService } from './dynamodb.service';

@Injectable()
export class RequestIdMiddleware implements NestMiddleware {
  constructor(private dynamoDBService: DynamoDBService) {}
  async use(req: Request, res: Response, next: NextFunction) {
    const requestId = uuid.v4();
    const apikey = req?.headers['x-api-key'];
    if (apikey) {
      const params = {
        TableName: process.env.CONSUMER_CONFIG_TABLE,
        Key: {
          PK: apikey,
        },
      };
      const data = await this.dynamoDBService.getObject(
        params.TableName,
        params.Key,
      );

      const retrievedItem = data.Item;
      req['brand'] = retrievedItem.brand;
    }

    req['requestId'] = requestId;

    res.setHeader('X-Request-ID', requestId);

    next();
  }
}
