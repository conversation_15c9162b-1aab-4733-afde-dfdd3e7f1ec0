import { Injectable } from '@nestjs/common';
import * as AWS from 'aws-sdk';
const s3 = new AWS.S3();
const sts = new AWS.STS();

@Injectable()
export class S3Service {
  async uploadObject(params): Promise<any> {
    const result = await s3.upload(params).promise();
    return result;
  }
  async deleteObject(params): Promise<any> {
    const result = await s3.deleteObject(params).promise();
    return result;
  }
  async getTextFileFromS3(key: string, bucketName: string): Promise<string> {
    try {
      const params: AWS.S3.GetObjectRequest = {
        Bucket: bucketName,
        Key: key,
      };

      const data = await s3.getObject(params).promise();
      return data.Body?.toString('utf-8') || '';
    } catch (error) {
      console.error('Error:', error);
      throw new Error('Failed to get text file from S3');
    }
  }
  async getObjectAsBase64ByRole(
    roleArn: string,
    bucketName: string,
    key: string,
  ): Promise<string> {
    try {
      const s3Client = await this.getS3CredentialsByRole(roleArn);

      const params: AWS.S3.GetObjectRequest = {
        Bucket: bucketName,
        Key: key,
      };

      const data = await s3Client.getObject(params).promise();
      return Buffer.from(data.Body as Buffer).toString('base64');
    } catch (error) {
      console.error('Error:', error);
      throw new Error('Failed to get Base64 object from S3');
    }
  }
  async getS3CredentialsByRole(roleArn): Promise<any> {
    const sessionName = `Session-${Date.now()}`;
    const param: AWS.STS.AssumeRoleRequest = {
      RoleArn: roleArn,
      RoleSessionName: sessionName,
    };
    const data = await new Promise((resolve, reject) => {
      sts.assumeRole(param, (err, data) => {
        if (err) reject(err);
        else resolve(data);
      });
    });
    const credentials = data['Credentials'];
    const s3 = new AWS.S3({
      accessKeyId: credentials.AccessKeyId,
      secretAccessKey: credentials.SecretAccessKey,
      sessionToken: credentials.SessionToken,
    });
    return s3;
  }
  async copyObject(
    sourceBucket: string,
    sourceKey: string,
    destinationKey: string,
    roleArn?: string,
  ): Promise<any> {
    try {
      let s3Client = s3;
      if (roleArn) {
        s3Client = await this.getS3CredentialsByRole(roleArn);
      }
      const params: AWS.S3.CopyObjectRequest = {
        Bucket: sourceBucket,
        CopySource: encodeURIComponent(`${sourceBucket}/${sourceKey}`),
        Key: destinationKey,
      };
      const result = await s3Client.copyObject(params).promise();
      return result;
    } catch (error) {
      console.error('Error copying object:', error);
      throw new Error('Failed to copy object in S3');
    }
  }

  async getFileBuffer(
    bucketName: string,
    key: string,
    roleArn?: string,
  ): Promise<Buffer> {
    try {
      let s3Client = s3;
      if (roleArn) {
        s3Client = await this.getS3CredentialsByRole(roleArn);
      }

      const params: AWS.S3.GetObjectRequest = {
        Bucket: bucketName,
        Key: key,
      };
      console.log('Params', params);

      const data = await s3Client.getObject(params).promise();
      return data.Body as Buffer;
    } catch (error) {
      console.error('Error getting file buffer from S3:', error);
      throw new Error(`Failed to get file from S3: ${error.message}`);
    }
  }

  async getUserMigrationFile(
    bucket: string,
    key: string,
    roleArn?: string,
  ): Promise<Buffer> {
    try {
      return await this.getFileBuffer(bucket, key, roleArn);
    } catch (error) {
      console.error('Error getting user migration file:', error);
      throw new Error(`Failed to retrieve user migration file: ${error.message}`);
    }
  }
}
