import { Injectable } from '@nestjs/common';
import axios from 'axios';

@Injectable()
export class ExternalService {
  async postData(requestData: any, requestedPath): Promise<any> {
    try {
      const response = await axios.post(requestedPath, requestData);

      return response.data;
    } catch (error) {
      throw error?.response?.data;
    }
  }
  async putData(requestData: any, requestedPath, headers): Promise<any> {
    try {
      const path = requestedPath;

      const response = await axios.put(path, requestData, { headers });

      return response?.data;
    } catch (error) {
      throw error?.response?.data;
    }
  }
}
