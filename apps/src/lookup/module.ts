import { forwardRef, Module } from '@nestjs/common';
import { LookUpController } from './controller';
import { LookUpService } from './service';
import { CommonModule } from '../common/module';
import { TranslationModule } from '../oapTranslation/module';
import { OapModule } from '../oap/module';
@Module({
  imports: [CommonModule, TranslationModule, forwardRef(() => OapModule)],
  controllers: [LookUpController],
  providers: [LookUpController, LookUpService],
  exports: [LookUpController, LookUpService],
})
export class LookUpModule { }
