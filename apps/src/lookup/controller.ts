import {
  Controller,
  Get,
  Post,
  Body,
  HttpException,
  HttpStatus,
  Param,
  Headers,
  Query,
  Req,
} from '@nestjs/common';
import { LookUpService } from './service';
import { Request } from 'express';
@Controller('oap/lookup')
export class LookUpController {
  constructor(private readonly lookUpService: LookUpService) {}
  @Get('/level')
  async filterData(
    @Headers('x-api-key') APIKEY: string,
    @Req() request: Request,
    @Query('displayLanguage') lang: string,
  ): Promise<any> {
    const response = await this.lookUpService.getLevel(APIKEY, request);
    if (lang && lang !== 'en') {
      const brand = await this.lookUpService.getBrandFromApiKey(APIKEY);
      return await this.lookUpService.translateAndMergePicklist(
        lang,
        `level#${brand}`,
        response,
      );
    }
    return response;
  }
  @Get('/country')
  async getCountry(
    @Query('brand') brand: string,
    @Headers('x-api-key') APIKEY: string,
    @Req() request: Request,
  ): Promise<any> {
    return await this.lookUpService.getCountry(APIKEY, request, brand);
  }
  @Get('/institutions/:country')
  async getInstitutionNamesByCountry(
    @Param('country') country: string,
    @Req() request: Request,
  ): Promise<any> {
    return await this.lookUpService.getInstitutionNamesByCountry(country);
  }
  @Get('/languages')
  async getLanguages(
    @Headers('x-api-key') APIKEY: string,
    @Req() request: Request,
  ): Promise<any> {
    return await this.lookUpService.getLanguages(APIKEY, request);
  }
  @Get('/programlanguages')
  async getProgramLanguages(
    @Headers('x-api-key') APIKEY: string,
    @Query() event: any,
    @Req() request: Request,
    @Query('displayLanguage') lang: string,
  ): Promise<any> {
    const response = await this.lookUpService.getProgramLanguages(event, APIKEY, request);
    if (lang && lang !== 'en') {
      const brand = await this.lookUpService.getBrandFromApiKey(APIKEY);
      return await this.lookUpService.translateAndMergePicklist(
        lang,
        `programlanguage#${brand}`,
        response,
      );
    }
    return response;
  }

  @Get('/programbylanguage')
  async getProgramByLevelAndLanguage(
    @Headers('x-api-key') APIKEY: string,
    @Query() event: any,
    @Req() request: Request,
    @Query('displayLanguage') lang: string,
  ): Promise<any> {
    const response = await this.lookUpService.getProgramByLevelAndLanguage(
      event,
      APIKEY,
      request,
    );
    if (lang && lang !== 'en') {
      const brand = await this.lookUpService.getBrandFromApiKey(APIKEY);
      return await this.lookUpService.translateAndMergePicklist(
        lang,
        `programBylanguage#${brand}`,
        response,
      );
    }
    return response;
  }

  @Get('/programlocations')
  async getProgramLocations(
    @Headers('x-api-key') APIKEY: string,
    @Query() event: any,
    @Req() request: Request,
    @Query('displayLanguage') lang: string,
  ): Promise<any> {
    const response = await this.lookUpService.getProgramLocations(event, APIKEY, request);
    if (lang && lang !== 'en') {
      const brand = await this.lookUpService.getBrandFromApiKey(APIKEY);
      return await this.lookUpService.translateAndMergePicklist(
        lang,
        `programlocations#${brand}`,
        response,
      );
    }
    return response;
  }

  @Get('/programduration')
  async getProgramDuration(
    @Headers('x-api-key') APIKEY: string,
    @Query() event: any,
    @Req() request: Request,
  ): Promise<any> {
    return await this.lookUpService.getProgramDuration(event, APIKEY, request);
  }

  @Get('/programintake')
  async getProgramIntake(
    @Headers('x-api-key') APIKEY: string,
    @Query() event: any,
    @Req() request: Request,
    @Query('displayLanguage') lang: string,
  ): Promise<any> {
    const response = await this.lookUpService.getProgramIntake(event, APIKEY, request);
    if (lang && lang !== 'en') {
      const brand = await this.lookUpService.getBrandFromApiKey(APIKEY);
      return await this.lookUpService.translateAndMergePicklist(
        lang,
        `programintake#${brand}`,
        response,
      );
    }
    return response;
  }

  @Get('/programmes')
  async getProgrammes(
    @Headers('x-api-key') APIKEY: string,
    @Query('email') email: string,
    @Query('brand') brand: string,
    @Query('byProduct') byProduct: boolean,
    @Req() request: Request,
  ): Promise<any> {
    return await this.lookUpService.getProgrammes(
      APIKEY,
      request,
      email,
      brand,
      byProduct,
    );
  }
  @Get('/pathwayproviders')
  async getPathwayProviders(
    @Headers('x-api-key') APIKEY: string,
    @Req() request: Request,
  ): Promise<any> {
    return await this.lookUpService.getPathwayProviders(APIKEY, request);
  }
  @Get('/states/:countryCode')
  async getStates(
    @Headers('x-api-key') APIKEY: string,
    @Param('countryCode') countryCode: string,
    @Req() request: Request,
  ): Promise<any> {
    try {
      return await this.lookUpService.getStates(countryCode, APIKEY, request);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('/programmes/:level')
  async getProgrammeByLevel(
    @Param('level') level: string,
    @Headers('x-api-key') APIKEY: string,
    @Query() event: any,
    @Req() request: Request,
  ): Promise<any> {
    try {
      return await this.lookUpService.getProgrammeByLevel(
        level,
        APIKEY,
        request,
        event,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Get('/studymodes/:programmeId')
  async getStudyModesByProgram(
    @Param('programmeId') programmeId: string,
    @Headers('x-api-key') APIKEY: string,
    @Req() request: Request,
  ): Promise<any> {
    try {
      return this.lookUpService.getStudyModesByProgramme(
        programmeId,
        APIKEY,
        request,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Get('/courseoptions/:programmeId')
  async getCourseOptionsByProgramme(
    @Param('programmeId') programmeId: string,
    @Query('studyMode') studyMode: string,
    @Headers('x-api-key') APIKEY: string,
    @Req() request: Request,
  ): Promise<any> {
    try {
      return this.lookUpService.getCourseOptionsByProgramme(
        programmeId,
        APIKEY,
        request,
        { StudyMode__c: studyMode },
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Get('/locations')
  async getLocations(
    @Headers('x-api-key') APIKEY: string,
    @Query('programId') programId: string,
    @Query('displayLanguage') lang: string,
    @Req() request: Request,
  ): Promise<any> {
    try {
      const response = await this.lookUpService.getLocations(
        APIKEY,
        request,
        programId,
      );
      if (lang && lang !== 'en') {
        const brand = await this.lookUpService.getBrandFromApiKey(APIKEY);
        return await this.lookUpService.translateAndMergePicklist(
          lang,
          `locations#${brand}`,
          response,
        );
      }
      return response;
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Get('/durations/:programmeId')
  async getDurationByProgramme(
    @Param('programmeId') programmeId: string,
    @Query('studyMode') studyMode: string,
    @Query('courseOption') courseOption: string,
    @Query('getValueFrom') getValueFrom: string,
    @Headers('x-api-key') APIKEY: string,
    @Req() request: Request,
  ): Promise<any> {
    try {
      // getting dynamic value from api query
      return this.lookUpService.getDurationByProgramme(
        programmeId,
        APIKEY,
        request,
        {
          StudyMode__c: studyMode,
          CourseOption__c: courseOption,
        },
        getValueFrom,
      );
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @Get('/intake/:programmeId')
  async getIntakeByProgrammeId(
    @Param('programmeId') programmeId: string,
    @Query() event: any,
    @Headers('x-api-key') APIKEY: string,
    @Req() request: Request,
  ): Promise<any> {
    try {
      if (Object.keys(event).length > 0) {
        const productFilter: any = {};
        const {
          studyMode,
          courseOption,
          duration,
          location,
          brand,
          programDelivery,
          email,
          productId,
          currentIntake,
        } = event;
        if (studyMode !== undefined) {
          productFilter.StudyMode__c = studyMode;
        }
        if (courseOption !== undefined) {
          productFilter.CourseOption__c = courseOption;
        }
        if (duration !== undefined && !isNaN(duration)) {
          productFilter.Campus_Days__c = Number(duration);
        }
        if (location !== undefined) {
          productFilter.Location__c = location;
        }
        if (programDelivery !== undefined) {
          productFilter.DeliveryName__c = programDelivery;
        }

        return this.lookUpService.getIntakeByProgrammeIdWithFilter(
          programmeId,
          APIKEY,
          { ...productFilter },
          request,
          brand,
          email,
          productId,
          currentIntake,
        );
      } else {
        // this block will be use by API without params
        return this.lookUpService.getIntakeByProgrammeId(
          programmeId,
          APIKEY,
          request,
        );
      }
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('/programdelivery')
  async getProgramDelivery(
    @Headers('x-api-key') APIKEY: string,
    @Query() event: any,
    @Req() request: Request,
    @Query('displayLanguage') lang: string,
  ): Promise<any> {
    const response = await this.lookUpService.getProgramDelivery(
      event,
      APIKEY,
      request,
    );
    if (lang && lang !== 'en') {
      const brand = await this.lookUpService.getBrandFromApiKey(APIKEY);
      return await this.lookUpService.translateAndMergePicklist(
        lang,
        `programdelivery#${brand}`,
        response,
      );
    }
    return response;
  }

  // Unified OAP Product Endpoint
  @Get('/product/:type')
  async getOapProduct(
    @Headers('x-api-key') APIKEY: string,
    @Param('type') type: string,
    @Query() queryParams: any,
    @Req() request: Request,
    @Query('displayLanguage') lang: string,
  ): Promise<any> {
    try {
      // Extract filter parameters from query
      const { displayLanguage, ...filters } = queryParams;

      // Validate type parameter
      const validTypes = ['location', 'level', 'language', 'programme', 'duration', 'intake', 'delivery'];
      if (!validTypes.includes(type)) {
        throw new HttpException(
          `Invalid type: ${type}. Supported types: ${validTypes.join(', ')}`,
          HttpStatus.BAD_REQUEST,
        );
      }

      // Convert filter values to proper format for Salesforce
      // Any combination of filters is allowed - middleware will handle the base conditions
      const formattedFilters: any = {};
      if (filters.location) formattedFilters.Location__c = filters.location;
      if (filters.level) formattedFilters['Programme__r.Level__c'] = filters.level;
      if (filters.language) formattedFilters.Language__c = filters.language;
      if (filters.programme) formattedFilters.Programme__c = filters.programme;
      if (filters.program) formattedFilters.Programme__c = filters.program; // Support both spellings
      if (filters.duration) formattedFilters.Duration__c = parseInt(filters.duration, 10); // Convert to integer
      if (filters.intake) formattedFilters.Intake__c = filters.intake;
      if (filters.delivery) formattedFilters.DeliveryName__c = filters.delivery;

      // Extract email, currentProductId and get brand from API key
      const email = filters.email;
      const currentProductId = filters.currentProductId;
      const brand = await this.lookUpService.getBrandFromApiKey(APIKEY);

      const response = await this.lookUpService.getOapProduct(type, formattedFilters, APIKEY, request, email, brand, currentProductId);

      if (lang && lang !== 'en') {
        const brand = await this.lookUpService.getBrandFromApiKey(APIKEY);
        return await this.lookUpService.translateAndMergePicklist(
          lang,
          `oap${type}#${brand}`,
          response,
        );
      }
      return response;
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
