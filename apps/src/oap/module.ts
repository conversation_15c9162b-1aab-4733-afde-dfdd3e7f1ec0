import {
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { OapController } from './controller';
import { OapService } from './service';
import { CommonModule } from '../common/module';
import { RequestIdMiddleware } from '../common/middleware';
import { pricebookModule } from '../pricebook/module';
import { LoggerModule } from '@gus-eip/loggers';
import { ConfigModule } from '@nestjs/config';
import { LookUpModule } from '../lookup/module';
import { TranslationModule } from '../oapTranslation/module';
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    LookUpModule,
    CommonModule,
    TranslationModule,
    pricebookModule,
    LoggerModule.forRoot({
      region: process.env.REGION,
      logGroupName: process.env.LOGGER_LOG_GROUP_NAME,
      teamWebhookUrl: process.env.TEAMS_WEBHOOK_URL,
      isAlertNeeded: true,
      options: 'CloudWatchLogger',
    }),
  ],
  controllers: [OapController],
  providers: [OapController, OapService],
  exports: [OapController, OapService],
})
export class OapModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(RequestIdMiddleware)
      .forRoutes({ path: '*', method: RequestMethod.ALL });
  }
}
