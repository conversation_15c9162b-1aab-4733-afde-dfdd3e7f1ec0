import { IsEmail, <PERSON>NotEmpty, IsO<PERSON>al, IsString, IsObject, ValidateNested, IsIn } from 'class-validator';
import { Type } from 'class-transformer';

export class RecaptchaVerificationDto {
  @IsNotEmpty()
  @IsString()
  token: string;

  @IsNotEmpty()
  @IsString()
  action: string;
}

// User Migration DTOs
export class S3ConfigDto {
  @IsNotEmpty()
  @IsString()
  bucket: string;

  @IsNotEmpty()
  @IsString()
  folder_path: string;

  @IsNotEmpty()
  @IsString()
  file_name: string;

  @IsNotEmpty()
  @IsString()
  @IsIn(['xlsx', 'csv'])
  file_type: 'xlsx' | 'csv';
}

export class CognitoConfigDto {
  @IsNotEmpty()
  @IsString()
  user_pool_id: string;

  @IsNotEmpty()
  @IsString()
  region: string;
}

export class UserMigrationConfigDto {
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => S3ConfigDto)
  s3_config: S3ConfigDto;

  @IsNotEmpty()
  @ValidateNested()
  @Type(() => CognitoConfigDto)
  cognito_config: CognitoConfigDto;

  @IsNotEmpty()
  @IsObject()
  column_mappings: Record<string, string>;
}

export class UserMigrationRequestDto {
  @IsNotEmpty()
  @IsString()
  oapName: string;

  @IsNotEmpty()
  @IsString()
  mode: string;
}

export class UserMigrationResultDto {
  email: string;
  status: 'success' | 'failed';
  error?: string;
  cognitoUserId?: string;
}

export class UserMigrationResponseDto {
  totalUsers: number;
  successfulMigrations: number;
  failedMigrations: number;
  results: UserMigrationResultDto[];
  configUsed: UserMigrationConfigDto;
}
