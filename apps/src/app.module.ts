import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule } from '@nestjs/config';
import { CommonModule } from '../src/common/module';
import { OapModule } from '../src/oap/module';
import { LookUpModule } from '../src/lookup/module';
import { pricebookModule } from '../src/pricebook/module';
import { NotificationModule } from './notification/module';
import { OapBuilderModule } from './oapbuilder/module';
import { TranslationModule } from './oapTranslation/module';
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    CommonModule,
    OapModule,
    LookUpModule,
    pricebookModule,
    NotificationModule,
    OapBuilderModule,
    TranslationModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
