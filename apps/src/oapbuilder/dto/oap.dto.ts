import {
  IsArray,
  ValidateNested,
  IsString,
  IsObject,
  IsNotEmpty,
} from 'class-validator';
import { Type } from 'class-transformer';
class StorageDetailsDto {
  @IsString()
  base64: string;
}

class LayoutDto {
  @IsString()
  backgroundColor: string;

  @ValidateNested()
  @Type(() => StorageDetailsDto)
  backgroundImage: StorageDetailsDto;

  @IsObject()
  collegeInfo: Record<string, any>;
}

class LogoInfoDto {
  @IsString()
  height: string;

  @IsString()
  width: string;

  @ValidateNested()
  @Type(() => StorageDetailsDto)
  storageDetails: StorageDetailsDto;
}

class PdfInfoDto {
  @IsArray()
  @IsString({ each: true })
  address: string[];

  @IsString()
  brand: string;

  @IsArray()
  images: StorageDetailsDto[];

  @IsObject()
  theme: Record<string, any>;
}

class PricebookDto {
  @IsString()
  id: string;

  @IsString()
  type: string;
}

class RecordTypeIdsDto {
  @IsString()
  account: string;

  @IsString()
  application: string;

  @IsString()
  contact: string;

  @IsString()
  lead: string;

  @IsString()
  opportunity: string;
}

class SubmissionEmailDetailsDto {
  @IsString()
  message: string;

  @IsString()
  subject: string;
}

class ThemeDto {
  @IsObject()
  colors: Record<string, any>;

  @IsObject()
  fontFamily: Record<string, any>;
}

export class OapDto {
  @IsNotEmpty()
  oap: string;

  @IsNotEmpty()
  mode: string;

  brand: string;
  brandCode: string;
  businessUnit: string;
  businessUnitFilter: string;
  eipConsumerKey: string;
  landingForm: string;
  latestStudentId: number;
  onSubmissionStage: string;
  preferedDateFormat: string;

  @ValidateNested()
  @Type(() => LayoutDto)
  layout: LayoutDto;

  @ValidateNested()
  @Type(() => LogoInfoDto)
  logoInfo: LogoInfoDto;

  @ValidateNested()
  @Type(() => PdfInfoDto)
  pdfInfo: PdfInfoDto;

  @ValidateNested({ each: true })
  @Type(() => PricebookDto)
  pricebooks: PricebookDto[];

  @ValidateNested()
  @Type(() => RecordTypeIdsDto)
  recordTypeIds: RecordTypeIdsDto;

  @ValidateNested()
  @Type(() => SubmissionEmailDetailsDto)
  submissionEmailDetails: SubmissionEmailDetailsDto;

  @ValidateNested()
  @Type(() => ThemeDto)
  theme: ThemeDto;
}
