import { Module } from '@nestjs/common';
import { CommonModule } from '../common/module';
import { ConfigModule } from '@nestjs/config';
import { OapBuilderService } from './service';
import { OapBuilderController } from './controller';
import { OapModule } from '../oap/module';
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    CommonModule,
    OapModule,
  ],
  controllers: [OapBuilderController],
  providers: [OapBuilderController, OapBuilderService],
  exports: [OapBuilderController],
})
export class OapBuilderModule {}
