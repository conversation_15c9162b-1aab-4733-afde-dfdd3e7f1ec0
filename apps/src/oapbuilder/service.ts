import { Injectable } from '@nestjs/common';
import { DynamoDBService } from '../common/dynamodb.service';
import { OapService } from '../oap/service';
@Injectable()
export class OapBuilderService {
  constructor(
    private readonly dynamodbService: DynamoDBService,
    private readonly oapService: OapService,
  ) {}
  async getOaps(): Promise<any> {
    try {
      const params = {
        TableName: process.env.GUS_OAP_TABLE,
        KeyConditionExpression: 'PK = :pkValue',
        ExpressionAttributeValues: {
          ':pkValue': `OAP`,
        },
      };
      const oapDetails = await this.dynamodbService.queryObjects(params);
      for (let oap of oapDetails.Items) {
        oap = await this.oapService.updateS3SignedUrl({ Item: oap });
      }
      return oapDetails;
    } catch (error) {
      throw error;
    }
  }
}
