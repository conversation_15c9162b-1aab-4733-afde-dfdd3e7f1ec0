import {
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Post,
  Body,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { OapBuilderService } from './service';
import { OapDto } from './dto/oap.dto';
@Controller('oap')
export class OapBuilderController {
  constructor(private readonly oapBuilderService: OapBuilderService) {}
  @Get('/list')
  async getByOap(): Promise<any> {
    try {
      const getOapDetails = await this.oapBuilderService.getOaps();
      return getOapDetails?.Items;
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
  @UsePipes(new ValidationPipe())
  @Post('')
  async persistOapDetails(@Body() data: OapDto): Promise<any> {
    try {
      console.log('data', data);
      // const getOapDetails = await this.oapBuilderService.getOaps();
      // return getOapDetails?.Items;
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
