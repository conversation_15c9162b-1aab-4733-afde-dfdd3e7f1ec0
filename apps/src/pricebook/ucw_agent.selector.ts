import { Injectable } from '@nestjs/common';
import { IPriceBookSelector } from './interface';

@Injectable()
export class UcwAgentPriceBookSelector implements IPriceBookSelector {
  async getPriceBook(details: any): Promise<string | null> {
    const pricebooks = details.oapDetails.pricebooks;

    const priceBookId = pricebooks?.filter(
      (item: IPriceBook) => item.type === 'default',
    );
    return priceBookId[0]?.id;
  }
}

interface IPriceBook {
  id: string;
  type: string;
}
