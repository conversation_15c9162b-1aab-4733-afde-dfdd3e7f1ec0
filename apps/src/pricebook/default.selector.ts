import { Injectable, Inject } from '@nestjs/common';
import { IPriceBookSelector } from './interface';

@Injectable()
export class DefaultPriceBookSelector implements IPriceBookSelector {
  async getPriceBook(details): Promise<string | null> {
    const pricebooks = details.oapDetails.pricebooks;
    const pricebookDetails = pricebooks.filter(
      (item): item is IPriceBook => item.type === 'default',
    );
    return pricebookDetails[0].id;
  }
}

interface IPriceBook {
  id: string;
  type: string;
}
