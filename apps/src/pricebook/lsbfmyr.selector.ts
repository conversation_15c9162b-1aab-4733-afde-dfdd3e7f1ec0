import { Injectable, Inject } from '@nestjs/common';
import { IPriceBookSelector } from './interface';

@Injectable()
export class LsbfmyrPriceBookSelector implements IPriceBookSelector {
    async getPriceBook(details): Promise<string | null> {
        const pricebooks = details.oapDetails.pricebooks;
        const priceBookType =
            details?.applicantDetails?.nationalityDisplayName === 'Malaysia'
                ? 'Domestic'
                : 'International';
        const pricebookDetails = pricebooks.filter(
            (item: IPriceBook) => item.type === priceBookType,
        );
        return pricebookDetails[0].id;
    }
}

interface IPriceBook {
    id: string;
    type: string;
}
