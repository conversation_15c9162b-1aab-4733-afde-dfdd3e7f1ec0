import { Modu<PERSON> } from '@nestjs/common';
import { IbatElPriceBookSelector } from './ibat_el.seclector';
import { LimPriceBookSelector } from './lim.seclector';
import { HzuPriceBookSelector } from './hzu.selector';
import { CommonModule } from '../common/module';
import { UcwAgentPriceBookSelector } from './ucw_agent.selector';
import { UnfcAgentPriceBookSelector } from './unfc_agent.selector';
import { LsbfmyrPriceBookSelector } from './lsbfmyr.selector';
import { DefaultPriceBookSelector } from './default.selector';
import { WulPriceBookSelector } from './wul.selector';


@Module({
  imports: [CommonModule],
  providers: [
    IbatElPriceBookSelector,
    LimPriceBookSelector,
    HzuPriceBookSelector,
    UcwAgentPriceBookSelector,
    UnfcAgentPriceBookSelector,
    LsbfmyrPriceBookSelector,
    DefaultPriceBookSelector,
    WulPriceBookSelector
  ],
  exports: [
    IbatElPriceBookSelector,
    LimPriceBookSelector,
    HzuPriceBookSelector,
    UcwAgentPriceBookSelector,
    UnfcAgentPriceBookSelector,
    LsbfmyrPriceBookSelector,
    DefaultPriceBookSelector,
    WulPriceBookSelector
  ],
})
export class pricebookModule { }
