import { Controller, Post, Body } from '@nestjs/common';
@Controller('')
export class NotificaitonController {
  @Post('/oap/createnotification')
  async createNotification(@Body() event): Promise<any> {
    console.log(event);
    return {
      createNotification: {
        createdAt: '2024-05-22T12:50:44.095Z',
        email: '<EMAIL>',
        event: 'ADMISSION_STATUS_UPDATE',
        message: 'Status changes',
        messageDetails: {
          messageId: '5efc8e8a-90b7-4e49-94c0-46958f76691c',
          opportunityId: '5efc8e8a',
        },
        readStatus: false,
        type: 'INAPP',
      },
    };
  }
}