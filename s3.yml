 Resources:
    R3ApplicationBucket:
      Type: AWS::S3::Bucket
      Properties:
        BucketName: oap-backend-${self:provider.stage}
        Tags:
          - Key: ENVIRONMENT
            Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
          - Key: TEAM
            Value: EIP Development Team
          - Key: PROJECT
            Value: OAP
    OapApplicationDocBucket:
      Type: AWS::S3::Bucket
      Properties:
        BucketName: oap-student-application-${self:provider.stage}
        Tags:
          - Key: ENVIRONMENT
            Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
          - Key: TEAM
            Value: EIP Development Team
          - Key: PROJECT
            Value: OAP