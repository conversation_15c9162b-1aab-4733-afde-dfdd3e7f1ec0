service: oap-backend-service
provider:
  name: aws
  runtime: nodejs20.x
  stage: ${opt:stage, 'dev'}
  region: eu-west-1
  role: ${file(env.${self:provider.stage}.yml):role_arn}
  ecr:
    images:
      appimage:
        path: ./
functions:
  oap-backend:
    name: 'oap-backend-service-${self:provider.stage}'
    architecture: x86_64
    image:
      name: appimage
      command:
        - dist/src/main.handler
      entryPoint:
        - '/lambda-entrypoint.sh'
    timeout: 120
    environment:
      ${file(env.${self:provider.stage}.yml):variables}
    tags:
      ENVIRONMENT: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
      TEAM: EIP Development Team
      PROJECT: OAP
resources:
  - ${file(permissions.yml)}
  - ${file(tables.yml)}
  - ${file(s3.yml)}
  - ${file(ecr.yml)}
  - ${file(ecr.yml)}