Resources:
  OapsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: gus-oaps-${self:provider.stage}
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
        - AttributeName: SK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
        - AttributeName: SK
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      Tags:
        - Key: ENVIRONMENT
          Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        - Key: TEAM
          Value: EIP Development Team
        - Key: PROJECT
          Value: OAP
  OapEventTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: oap-events-${self:provider.stage}
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST
      Tags:
        - Key: ENVIRONMENT
          Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        - Key: TEAM
          Value: EIP Development Team
        - Key: PROJECT
          Value: OAP
  OapFormsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: gus-oap-forms-${self:provider.stage}
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
        - AttributeName: SK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
        - AttributeName: SK
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      Tags:
        - Key: ENVIRONMENT
          Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        - Key: TEAM
          Value: EIP Development Team
        - Key: PROJECT
          Value: OAP
  OapFormSectionsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: gus-oap-form-sections-${self:provider.stage}
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
        - AttributeName: SK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
        - AttributeName: SK
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      Tags:
        - Key: ENVIRONMENT
          Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        - Key: TEAM
          Value: EIP Development Team
        - Key: PROJECT
          Value: OAP
  oapStudentDetailsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: gus-oap-student-applications-${self:provider.stage}
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
        - AttributeName: SK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
        - AttributeName: SK
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      GlobalSecondaryIndexes:
        - IndexName: applicationId
          KeySchema:
            - AttributeName: SK
              KeyType: HASH
          Projection:
            ProjectionType: ALL
      Tags:
        - Key: ENVIRONMENT
          Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        - Key: TEAM
          Value: EIP Development Team
        - Key: PROJECT
          Value: OAP
  oapStudentDocsTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: gus-oap-student-application-documents-${self:provider.stage}
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
        - AttributeName: SK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
        - AttributeName: SK
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      Tags:
        - Key: ENVIRONMENT
          Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        - Key: TEAM
          Value: EIP Development Team
        - Key: PROJECT
          Value: OAP
  oapBrandTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: gus-oap-brands-${self:provider.stage}
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
      BillingMode: PAY_PER_REQUEST
      Tags:
        - Key: ENVIRONMENT
          Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        - Key: TEAM
          Value: EIP Development Team
        - Key: PROJECT
          Value: OAP
  oapStudentDetails:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: gus-oap-student-details-${self:provider.stage}
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
        - AttributeName: SK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
        - AttributeName: SK
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      Tags:
        - Key: ENVIRONMENT
          Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        - Key: TEAM
          Value: EIP Development Team
        - Key: PROJECT
          Value: OAP
  oapLocalization:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: gus-oap-localization-${self:provider.stage}
      AttributeDefinitions:
        - AttributeName: PK
          AttributeType: S
        - AttributeName: SK
          AttributeType: S
      KeySchema:
        - AttributeName: PK
          KeyType: HASH
        - AttributeName: SK
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      Tags:
        - Key: ENVIRONMENT
          Value: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
        - Key: TEAM
          Value: EIP Development Team
        - Key: PROJECT
          Value: OAP
